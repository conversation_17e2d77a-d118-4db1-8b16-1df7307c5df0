import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { SafeAreaView, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { homeScreenStyles } from './styles';

export default function HomeScreen() {
  const { user, signOut } = useAuth();

  // Mock data - in real app, this would come from database
  const [savingsData, setSavingsData] = useState({
    currentAmount: 2500000,
    targetAmount: 10000000,
    selectedAgent: 'Ibu Sari',
    agentPhone: '0812-3456-7890'
  });

  const progressPercentage = Math.round((savingsData.currentAmount / savingsData.targetAmount) * 100);

  const handleSignOut = async () => {
    await signOut();
    router.replace('/(auth)/login');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <SafeAreaView style={homeScreenStyles.container}>
      <ScrollView contentContainerStyle={homeScreenStyles.scrollContent}>
        {/* Header */}
        <View style={homeScreenStyles.header}>
          <Text style={homeScreenStyles.appTitle}>SaveMoney</Text>
          <TouchableOpacity style={homeScreenStyles.signOutButton} onPress={handleSignOut}>
            <Ionicons name="log-out-outline" size={24} color="#fff" />
            <Text style={homeScreenStyles.signOutText}>Keluar</Text>
          </TouchableOpacity>
        </View>

        {/* Welcome Message */}
        <View style={homeScreenStyles.welcomeSection}>
          <Text style={homeScreenStyles.welcomeText}>Selamat Datang!</Text>
          <Text style={homeScreenStyles.userEmail}>{user?.email}</Text>
        </View>

        {/* Progress Tabungan */}
        <View style={homeScreenStyles.progressSection}>
          <Text style={homeScreenStyles.sectionTitle}>Progress Tabungan</Text>

          <View style={homeScreenStyles.progressContainer}>
            <View style={homeScreenStyles.progressBar}>
              <View
                style={[
                  homeScreenStyles.progressFill,
                  { width: `${Math.min(progressPercentage, 100)}%` }
                ]}
              />
            </View>
            <Text style={homeScreenStyles.progressText}>{progressPercentage}%</Text>
          </View>

          <View style={homeScreenStyles.amountInfo}>
            <Text style={homeScreenStyles.currentAmountLabel}>Tabungan Saat Ini:</Text>
            <Text style={homeScreenStyles.currentAmount}>{formatCurrency(savingsData.currentAmount)}</Text>
            <Text style={homeScreenStyles.targetAmountLabel}>Target: {formatCurrency(savingsData.targetAmount)}</Text>
          </View>
        </View>

        {/* Agent Information */}
        <View style={styles.agentSection}>
          <Text style={styles.sectionTitle}>Agent Anda</Text>
          <View style={styles.agentCard}>
            <View style={styles.agentIcon}>
              <Ionicons name="person" size={32} color="#4CAF50" />
            </View>
            <View style={styles.agentInfo}>
              <Text style={styles.agentName}>{savingsData.selectedAgent}</Text>
              <Text style={styles.agentPhone}>{savingsData.agentPhone}</Text>
            </View>
            <TouchableOpacity style={styles.callButton}>
              <Ionicons name="call" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingVertical: 10,
  },
  appTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF5722',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 25,
    gap: 8,
  },
  signOutText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  welcomeSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2E7D32',
    textAlign: 'center',
    marginBottom: 8,
  },
  userEmail: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  progressSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 15,
    textAlign: 'center',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    gap: 15,
  },
  progressBar: {
    flex: 1,
    height: 20,
    backgroundColor: '#E0E0E0',
    borderRadius: 10,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 10,
  },
  progressText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    minWidth: 50,
    textAlign: 'center',
  },
  amountInfo: {
    alignItems: 'center',
  },
  currentAmountLabel: {
    fontSize: 16,
    color: '#666',
    marginBottom: 5,
  },
  currentAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 10,
  },
  targetAmountLabel: {
    fontSize: 14,
    color: '#999',
  },
  agentSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  agentCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#F1F8E9',
    borderRadius: 12,
    gap: 15,
  },
  agentIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  agentInfo: {
    flex: 1,
  },
  agentName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 4,
  },
  agentPhone: {
    fontSize: 16,
    color: '#666',
  },
  callButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 4,
  },
});
