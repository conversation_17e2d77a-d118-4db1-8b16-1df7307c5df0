import { router } from 'expo-router';
import React, { useState } from 'react';
import { Alert } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import AuthForm from './components/AuthForm';

export default function RegisterScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const { signUp } = useAuth();

  const handleRegister = async () => {
    if (!email || !password || !confirmPassword) {
      Alert.alert('Kesalahan', 'Harap isi semua kolom');
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert('Kesalahan', 'Kata sandi tidak cocok');
      return;
    }

    if (password.length < 6) {
      Alert.alert('<PERSON>salahan', 'Kata sandi harus minimal 6 karakter');
      return;
    }

    setLoading(true);
    const { error } = await signUp(email, password);

    if (error) {
      Alert.alert('Kesalahan Pendaftaran', error.message);
    } else {
      Alert.alert(
        'Berhasil',
        'Pendaftaran berhasil! Silakan periksa email Anda untuk verifikasi.',
        [
          {
            text: 'OK',
            onPress: () => router.replace('/(beranda)'),
          },
        ]
      );
    }
    setLoading(false);
  };

  return (
    <AuthForm
      title="Buat Akun"
      subtitle="Daftar untuk memulai"
      email={email}
      password={password}
      confirmPassword={confirmPassword}
      loading={loading}
      buttonText="Daftar"
      loadingText="Membuat Akun..."
      linkText="Sudah punya akun?"
      linkHref="/(auth)/login"
      linkButtonText="Masuk"
      showConfirmPassword={true}
      onEmailChange={setEmail}
      onPasswordChange={setPassword}
      onConfirmPasswordChange={setConfirmPassword}
      onSubmit={handleRegister}
    />
  );
}


